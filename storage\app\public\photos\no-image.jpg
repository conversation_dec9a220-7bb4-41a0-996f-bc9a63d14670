<?xml version="1.0" encoding="UTF-8"?>
<svg width="300" height="200" xmlns="http://www.w3.org/2000/svg">
<defs>
<style>
.modern-gradient { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.text-modern { font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; }
</style>
<linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
<stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
<stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
</linearGradient>
<linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
<stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
<stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
</linearGradient>
<linearGradient id="secondaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
<stop offset="0%" style="stop-color:#4f46e5;stop-opacity:1" />
<stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
</linearGradient>
<filter id="dropShadow" x="-50%" y="-50%" width="200%" height="200%">
<feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.1"/>
</filter>
</defs>

  <!-- Background -->
  <rect width="100%" height="100%" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="2"/>
  
  <!-- Icon -->
  <circle cx="150" cy="80" r="25" fill="#cbd5e1"/>
  <path d="M135 70 L165 70 L165 90 L135 90 Z" fill="#94a3b8"/>
  <circle cx="145" cy="78" r="3" fill="#64748b"/>
  <path d="M140 85 L150 80 L160 85 L165 90 L135 90 Z" fill="#64748b"/>
  
  <!-- Text -->
  <text x="150" y="130" text-anchor="middle" class="text-modern" font-size="14" font-weight="500" fill="#64748b">No Image Available</text>
  <text x="150" y="150" text-anchor="middle" class="text-modern" font-size="12" fill="#94a3b8">Image not found</text>

</svg>